"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[lng]/(dashboard)/content-manager/[...slug]/page",{

/***/ "(app-pages-browser)/./src/layouts/builder/page/LeftSidebarLayout.tsx":
/*!********************************************************!*\
  !*** ./src/layouts/builder/page/LeftSidebarLayout.tsx ***!
  \********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LeftSidebarLayout: function() { return /* binding */ LeftSidebarLayout; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_define_property__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @swc/helpers/_/_define_property */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_define_property.js\");\n/* harmony import */ var _swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @swc/helpers/_/_object_spread */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_spread.js\");\n/* harmony import */ var _swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @swc/helpers/_/_object_spread_props */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_spread_props.js\");\n/* harmony import */ var _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @swc/helpers/_/_sliced_to_array */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_sliced_to_array.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Icon,useIsomorphicLayoutEffect!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/hooks/useIsomorphicLayoutEffect.js\");\n/* harmony import */ var _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Icon,useIsomorphicLayoutEffect!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/components/Icon/Icon.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(app-pages-browser)/../../node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_Builder__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/Builder */ \"(app-pages-browser)/./src/components/Builder/FieldEditor/FieldEditor.tsx\");\n/* harmony import */ var _components_Builder__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/Builder */ \"(app-pages-browser)/./src/components/Builder/ComponentList/ComponentList.tsx\");\n/* harmony import */ var _contexts_BuilderContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/BuilderContext */ \"(app-pages-browser)/./src/contexts/BuilderContext.tsx\");\n/* harmony import */ var _LayerSidebarLayout__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./LayerSidebarLayout */ \"(app-pages-browser)/./src/layouts/builder/page/LayerSidebarLayout.tsx\");\n/* harmony import */ var _pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pagebuilderlayout.module.scss */ \"(app-pages-browser)/./src/layouts/builder/page/pagebuilderlayout.module.scss\");\n/* harmony import */ var _pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nvar _this = undefined;\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nvar LeftSidebarLayout = function() {\n    var _childComponentData_;\n    _s();\n    var context = (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(_contexts_BuilderContext__WEBPACK_IMPORTED_MODULE_4__.PageBuilderContext);\n    var expandedSidebar = context.expandedSidebar, editingIden = context.editingIden, data = context.data, components = context.components, setData = context.setData, setEditingIden = context.setEditingIden, childComponentData = context.childComponentData, setChildComponentData = context.setChildComponentData, setActiveMediaId = context.setActiveMediaId, setMediaInfoData = context.setMediaInfoData;\n    var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)((0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(-1), 2), componentToEditId = _useState[0], setComponentToEditId = _useState[1];\n    var _useState1 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)((0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(), 2), cmpData = _useState1[0], setCmpData = _useState1[1];\n    // Filtered component's attributes\n    var filteredComponents = function(obj) {\n        return Object.entries(obj || {}).filter(function(param) {\n            var _param = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)(param, 2), value = _param[1];\n            return typeof value === \"object\" && value !== null;\n        });\n    };\n    // Get sectionData directly from data on each render to always have the latest data\n    var sectionData = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(function() {\n        return data === null || data === void 0 ? void 0 : data.data.components.find(function(component) {\n            return component.id === editingIden.id || component.__temp_key__ === editingIden.id;\n        });\n    }, [\n        data,\n        editingIden\n    ]);\n    (0,_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_6__.useIsomorphicLayoutEffect)(function() {\n        setComponentToEditId(editingIden.id);\n    }, [\n        editingIden\n    ]);\n    (0,_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_6__.useIsomorphicLayoutEffect)(function() {\n        setCmpData(components.data.find(function(comp) {\n            return comp.uid === (sectionData === null || sectionData === void 0 ? void 0 : sectionData.__component);\n        }));\n    }, [\n        sectionData,\n        components.data\n    ]);\n    (0,_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_6__.useIsomorphicLayoutEffect)(function() {\n        !expandedSidebar.left && setChildComponentData([]);\n    }, [\n        expandedSidebar.left\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().sidebar), !expandedSidebar.left && (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().is__hidden)),\n        children: cmpData && sectionData && componentToEditId !== -1 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().component__wrapper),\n            children: [\n                childComponentData && ((_childComponentData_ = childComponentData[0]) === null || _childComponentData_ === void 0 ? void 0 : _childComponentData_.name) !== \"\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LayerSidebarLayout__WEBPACK_IMPORTED_MODULE_7__.LayerSidebarLayout, {}, void 0, false, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LeftSidebarLayout.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 67\n                }, _this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().component__title),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: function() {\n                                // Close MediaInfoLayer when closing LayerSidebarLayout\n                                setActiveMediaId(null);\n                                setMediaInfoData({\n                                    name: \"\",\n                                    url: \"\"\n                                });\n                                setEditingIden({\n                                    key: \"\",\n                                    id: -1\n                                });\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_8__.Icon, {\n                                type: \"cms\",\n                                variant: \"chevron-left\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LeftSidebarLayout.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 8\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LeftSidebarLayout.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 7\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                            className: \"collect__heading collect__heading--h5\",\n                            children: (cmpData === null || cmpData === void 0 ? void 0 : cmpData.schema.displayName) || cmpData.uid\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LeftSidebarLayout.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 7\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LeftSidebarLayout.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 6\n                }, _this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().editor__components),\n                    children: filteredComponents(cmpData.schema.attributes).map(function(param) {\n                        var _param = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)(param, 2), key = _param[0], value = _param[1];\n                        var val = value;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Builder__WEBPACK_IMPORTED_MODULE_9__.FieldEditor, (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_10__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_11__._)({}, val), {\n                            name: key,\n                            size: 12,\n                            layerPos: \"left\",\n                            value: sectionData[key],\n                            onChange: function(props) {\n                                setData(function(prevData) {\n                                    var newData = (0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_11__._)({}, prevData);\n                                    var field = props.field, value = props.value;\n                                    var curCmpIndex = newData.data.components.findIndex(function(data) {\n                                        return data.__component === editingIden.key && data.id === editingIden.id;\n                                    });\n                                    if (curCmpIndex === -1) {\n                                        return newData;\n                                    }\n                                    newData.data.components[curCmpIndex] = (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_10__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_11__._)({}, newData.data.components[curCmpIndex]), (0,_swc_helpers_define_property__WEBPACK_IMPORTED_MODULE_12__._)({}, field.trim(), value));\n                                    // console.log(`[FieldEditor] New data after update:`, newData)\n                                    return newData;\n                                });\n                            }\n                        }), key, false, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LeftSidebarLayout.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 9\n                        }, _this);\n                    })\n                }, void 0, false, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LeftSidebarLayout.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 6\n                }, _this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LeftSidebarLayout.tsx\",\n            lineNumber: 54,\n            columnNumber: 5\n        }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Builder__WEBPACK_IMPORTED_MODULE_13__.ComponentList, {}, void 0, false, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LeftSidebarLayout.tsx\",\n            lineNumber: 107,\n            columnNumber: 5\n        }, _this)\n    }, void 0, false, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LeftSidebarLayout.tsx\",\n        lineNumber: 52,\n        columnNumber: 3\n    }, _this);\n};\n_s(LeftSidebarLayout, \"JQC0MW6nnBgM18/suWAQ8+tf9r8=\", false, function() {\n    return [\n        _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_6__.useIsomorphicLayoutEffect,\n        _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_6__.useIsomorphicLayoutEffect,\n        _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_6__.useIsomorphicLayoutEffect\n    ];\n});\n_c = LeftSidebarLayout;\nvar _c;\n$RefreshReg$(_c, \"LeftSidebarLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/layouts/builder/page/LeftSidebarLayout.tsx\n"));

/***/ })

});