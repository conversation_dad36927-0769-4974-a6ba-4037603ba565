"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[lng]/(pagebuilder)/content-builder/[...slug]/layout",{

/***/ "(app-pages-browser)/./src/components/Builder/LayoutEditor/LayoutEditor.tsx":
/*!**************************************************************!*\
  !*** ./src/components/Builder/LayoutEditor/LayoutEditor.tsx ***!
  \**************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LayoutEditor: function() { return /* binding */ LayoutEditor; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_define_property__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @swc/helpers/_/_define_property */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_define_property.js\");\n/* harmony import */ var _swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @swc/helpers/_/_object_spread */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_spread.js\");\n/* harmony import */ var _swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @swc/helpers/_/_object_spread_props */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_spread_props.js\");\n/* harmony import */ var _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @swc/helpers/_/_sliced_to_array */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_sliced_to_array.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_useWindowDimensions_collective_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Icon,useIsomorphicLayoutEffect,useWindowDimensions!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/hooks/useWindowDimensions.js\");\n/* harmony import */ var _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_useWindowDimensions_collective_core__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Icon,useIsomorphicLayoutEffect,useWindowDimensions!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/hooks/useIsomorphicLayoutEffect.js\");\n/* harmony import */ var _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_useWindowDimensions_collective_core__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Icon,useIsomorphicLayoutEffect,useWindowDimensions!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/components/Icon/Icon.js\");\n/* harmony import */ var _collective_ui_lib_src_base_Wrapper__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @collective/ui-lib/src/base/Wrapper */ \"(app-pages-browser)/../../packages/ui-lib/src/base/Wrapper.tsx\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(app-pages-browser)/../../node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _contexts_BuilderContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/BuilderContext */ \"(app-pages-browser)/./src/contexts/BuilderContext.tsx\");\n/* harmony import */ var _ComponentMenu__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../ComponentMenu */ \"(app-pages-browser)/./src/components/Builder/ComponentMenu/ComponentMenu.tsx\");\n/* harmony import */ var _ComponentQuickActions__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../ComponentQuickActions */ \"(app-pages-browser)/./src/components/Builder/ComponentQuickActions/ComponentQuickActions.tsx\");\n/* harmony import */ var _Dnd__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../Dnd */ \"(app-pages-browser)/./src/components/Builder/Dnd/Board.tsx\");\n/* harmony import */ var _layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./layouteditor.module.scss */ \"(app-pages-browser)/./src/components/Builder/LayoutEditor/layouteditor.module.scss\");\n/* harmony import */ var _layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nvar _this = undefined;\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nvar LayoutEditor = function(param) {\n    var children = param.children;\n    _s();\n    var _s1 = $RefreshSig$();\n    var context = (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(_contexts_BuilderContext__WEBPACK_IMPORTED_MODULE_4__.PageBuilderContext);\n    var data = context.data, contentType = context.contentType, configuration = context.configuration, setData = context.setData, components = context.components, setEditingIden = context.setEditingIden, normalizedData = context.normalizedData, setActiveMediaId = context.setActiveMediaId, setMediaInfoData = context.setMediaInfoData;\n    var _ref = data !== null && data !== void 0 ? data : {}, commonData = _ref.data;\n    var _ref1 = contentType !== null && contentType !== void 0 ? contentType : {}, uidConfig = _ref1.data;\n    var _ref2 = components !== null && components !== void 0 ? components : {}, uiConfig = _ref2.data;\n    var windowDimension = (0,_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_useWindowDimensions_collective_core__WEBPACK_IMPORTED_MODULE_5__.useWindowDimensions)();\n    // Handle Headline change\n    var globalField = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(function() {\n        if (!contentType.data || !configuration.data) return \"\";\n        var settings = configuration.data.contentType.settings;\n        var mainFieldKey = settings.mainField;\n        return mainFieldKey;\n    }, [\n        contentType,\n        configuration\n    ]);\n    var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_6__._)((0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(commonData && globalField ? commonData[globalField] : \"\"), 2), headline = _useState[0], setHeadline = _useState[1];\n    var textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    var handleChange = function(event) {\n        if (!textareaRef.current) return;\n        var target = event.target;\n        setHeadline(target.value);\n        setData((0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_7__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, data), {\n            data: (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_7__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, data.data), (0,_swc_helpers_define_property__WEBPACK_IMPORTED_MODULE_9__._)({}, globalField, target.value))\n        }));\n        textareaRef.current.style.height = \"auto\";\n        textareaRef.current.style.height = textareaRef.current.scrollHeight + \"px\";\n    };\n    (0,_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_useWindowDimensions_collective_core__WEBPACK_IMPORTED_MODULE_10__.useIsomorphicLayoutEffect)(function() {\n        if (!textareaRef.current) return;\n        textareaRef.current.style.height = \"auto\";\n        textareaRef.current.style.height = textareaRef.current.scrollHeight + \"px\";\n    }, [\n        textareaRef,\n        windowDimension\n    ]);\n    // Handle component menu\n    var _useState1 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_6__._)((0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null), 2), menu = _useState1[0], setMenu = _useState1[1];\n    var triggerMenu = function(e) {\n        var container = e.currentTarget;\n        setMenu(function(prev) {\n            return prev !== container ? container : null;\n        });\n    };\n    var handleAddBlock = function(component) {\n        if (!component) return setMenu(null);\n        var id = Number(menu === null || menu === void 0 ? void 0 : menu.dataset.id);\n        var defaultData = uiConfig.find(function(item) {\n            return item.uid === component.uid;\n        });\n        var attributes = defaultData === null || defaultData === void 0 ? void 0 : defaultData.schema.attributes;\n        if (!attributes) return setMenu(null);\n        var remapProps = Object.entries(attributes).reduce(function(acc, param) {\n            var _param = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_6__._)(param, 2), key = _param[0], value = _param[1];\n            var newValue;\n            switch(value.type){\n                case \"boolean\":\n                    var _value_default;\n                    newValue = (_value_default = value[\"default\"]) !== null && _value_default !== void 0 ? _value_default : false;\n                    break;\n                case \"string\":\n                    newValue = \"\";\n                    break;\n                default:\n                    newValue = null;\n                    break;\n            }\n            acc[key] = newValue;\n            return acc;\n        }, {});\n        var addData = (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_7__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({\n            __component: component.uid\n        }, remapProps), {\n            __temp_key__: normalizedData.components.length + 1\n        });\n        var components = normalizedData.components;\n        var index = components.findIndex(function(component) {\n            return component.__temp_key__ === id;\n        });\n        components.splice(index + 1, 0, addData);\n        setData((0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_7__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, data), {\n            data: (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_7__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, data.data), {\n                components: components\n            })\n        }));\n        setMenu(null);\n    };\n    // Get list available components\n    var Modules = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(function() {\n        if (!uidConfig) return {};\n        if (!uidConfig.schema.attributes.components) return {};\n        if (\"components\" in uidConfig.schema.attributes.components === false) return {};\n        var components = {};\n        var arrComponents = uidConfig.schema.attributes.components.components;\n        arrComponents === null || arrComponents === void 0 ? void 0 : arrComponents.forEach(function(module) {\n            var Component = (0,_collective_ui_lib_src_base_Wrapper__WEBPACK_IMPORTED_MODULE_11__.CmsWrapper)({\n                module: module\n            });\n            if (Component && components) components[module] = Component;\n        });\n        return components;\n    }, [\n        uidConfig\n    ]);\n    // Component wrapper with hover state\n    var ComponentWrapper = function(param) {\n        var column = param.column, index = param.index;\n        _s1();\n        var Module = (column === null || column === void 0 ? void 0 : column.__component) && Modules && Modules[column.__component];\n        var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_6__._)((0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), 2), isHovered = _useState[0], setIsHovered = _useState[1];\n        var handleEdit = function() {\n            if (!column) return;\n            var id = column.id || column.__temp_key__;\n            setEditingIden({\n                key: column.__component,\n                id: id\n            });\n        // setChildComponentData({ name: '', value: {}, fields: [['', {}] as Entry] })\n        };\n        // console.log(Modules)\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            tabIndex: 0,\n            role: \"button\",\n            className: (_layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3___default().component__block),\n            onClick: handleEdit,\n            onKeyDown: handleEdit,\n            onMouseEnter: function() {\n                return setIsHovered(true);\n            },\n            onMouseLeave: function() {\n                return setIsHovered(false);\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ComponentQuickActions__WEBPACK_IMPORTED_MODULE_12__.ComponentQuickActions, {\n                    index: index,\n                    id: column === null || column === void 0 ? void 0 : column.__temp_key__,\n                    isVisible: isHovered\n                }, void 0, false, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                    lineNumber: 170,\n                    columnNumber: 5\n                }, _this),\n                Module ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Module, (0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, column), void 0, false, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                    lineNumber: 176,\n                    columnNumber: 6\n                }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3___default().component__error)\n                }, void 0, false, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                    lineNumber: 178,\n                    columnNumber: 6\n                }, _this)\n            ]\n        }, column === null || column === void 0 ? void 0 : column.__temp_key__, true, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n            lineNumber: 160,\n            columnNumber: 4\n        }, _this);\n    };\n    _s1(ComponentWrapper, \"FPQn8a98tPjpohC7NUYORQR8GJE=\");\n    // Column component for Board\n    var ColumnComponent = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(props) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ComponentWrapper, (0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, props), void 0, false, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n            lineNumber: 191,\n            columnNumber: 11\n        }, _this);\n    }, // ComponentWrapper is defined in the component scope, so it doesn't need to be in the dependency array\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [\n        Modules,\n        setEditingIden\n    ]);\n    var ColumnAddBlock = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(param) {\n        var column = param.column;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n            \"data-id\": column === null || column === void 0 ? void 0 : column.__temp_key__,\n            className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"text__w--icon align__center add__block\", (_layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3___default().add__block)),\n            onClick: triggerMenu,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_useWindowDimensions_collective_core__WEBPACK_IMPORTED_MODULE_13__.Icon, {\n                    variant: \"plus-circle\",\n                    type: \"cms\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                    lineNumber: 205,\n                    columnNumber: 5\n                }, _this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: (_layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3___default().line)\n                }, void 0, false, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                    lineNumber: 206,\n                    columnNumber: 5\n                }, _this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n            lineNumber: 200,\n            columnNumber: 4\n        }, _this);\n    }, []);\n    // Toggle active when trigger menu\n    (0,_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_useWindowDimensions_collective_core__WEBPACK_IMPORTED_MODULE_10__.useIsomorphicLayoutEffect)(function() {\n        var allBlockBtn = document.querySelectorAll(\".add__block\");\n        var _iteratorNormalCompletion = true, _didIteratorError = false, _iteratorError = undefined;\n        try {\n            for(var _iterator = allBlockBtn[Symbol.iterator](), _step; !(_iteratorNormalCompletion = (_step = _iterator.next()).done); _iteratorNormalCompletion = true){\n                var button = _step.value;\n                button.classList.toggle(\"active\", menu === button);\n            }\n        } catch (err) {\n            _didIteratorError = true;\n            _iteratorError = err;\n        } finally{\n            try {\n                if (!_iteratorNormalCompletion && _iterator[\"return\"] != null) {\n                    _iterator[\"return\"]();\n                }\n            } finally{\n                if (_didIteratorError) {\n                    throw _iteratorError;\n                }\n            }\n        }\n    }, [\n        menu\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3___default().wrapper),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3___default().header),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"text__w--icon align__center add__block\", (_layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3___default().add__image)),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_useWindowDimensions_collective_core__WEBPACK_IMPORTED_MODULE_13__.Icon, {\n                                variant: \"image\",\n                                type: \"cms\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 6\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"collect__body--lg\",\n                                children: \"Add cover image\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 6\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                        lineNumber: 222,\n                        columnNumber: 5\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                        rows: 1,\n                        placeholder: \"Post Title\",\n                        ref: textareaRef,\n                        className: (_layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3___default().headline),\n                        value: headline,\n                        onChange: handleChange\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 5\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        \"data-id\": \"0\",\n                        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"text__w--icon align__center add__block\", (_layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3___default().add__block)),\n                        onClick: triggerMenu,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_useWindowDimensions_collective_core__WEBPACK_IMPORTED_MODULE_13__.Icon, {\n                                variant: \"plus-circle\",\n                                type: \"cms\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 6\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"collect__heading--h6\",\n                                children: \"Add block\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 6\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                        lineNumber: 234,\n                        columnNumber: 5\n                    }, _this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                lineNumber: 221,\n                columnNumber: 4\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Dnd__WEBPACK_IMPORTED_MODULE_14__.Board, {\n                initial: normalizedData === null || normalizedData === void 0 ? void 0 : normalizedData.components,\n                className: (_layouteditor_module_scss__WEBPACK_IMPORTED_MODULE_3___default().body),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ColumnComponent, {}, void 0, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                        lineNumber: 245,\n                        columnNumber: 5\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ColumnAddBlock, {}, void 0, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                        lineNumber: 246,\n                        columnNumber: 5\n                    }, _this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                lineNumber: 244,\n                columnNumber: 4\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ComponentMenu__WEBPACK_IMPORTED_MODULE_15__.ComponentMenu, {\n                trigger: menu,\n                onClose: handleAddBlock\n            }, void 0, false, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n                lineNumber: 249,\n                columnNumber: 4\n            }, _this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\LayoutEditor\\\\LayoutEditor.tsx\",\n        lineNumber: 220,\n        columnNumber: 3\n    }, _this);\n};\n_s(LayoutEditor, \"Lwv10dbcrNmFEGn3WObpwHKLwJY=\", false, function() {\n    return [\n        _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_useWindowDimensions_collective_core__WEBPACK_IMPORTED_MODULE_5__.useWindowDimensions,\n        _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_useWindowDimensions_collective_core__WEBPACK_IMPORTED_MODULE_10__.useIsomorphicLayoutEffect,\n        _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_useWindowDimensions_collective_core__WEBPACK_IMPORTED_MODULE_10__.useIsomorphicLayoutEffect\n    ];\n});\n_c = LayoutEditor;\nvar _c;\n$RefreshReg$(_c, \"LayoutEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Builder/LayoutEditor/LayoutEditor.tsx\n"));

/***/ })

});