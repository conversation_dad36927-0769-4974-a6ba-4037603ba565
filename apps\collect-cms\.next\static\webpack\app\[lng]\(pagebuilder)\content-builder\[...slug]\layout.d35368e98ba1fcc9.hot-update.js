"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[lng]/(pagebuilder)/content-builder/[...slug]/layout",{

/***/ "(app-pages-browser)/./src/layouts/builder/page/LayerSidebarLayout.tsx":
/*!*********************************************************!*\
  !*** ./src/layouts/builder/page/LayerSidebarLayout.tsx ***!
  \*********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LayerSidebarLayout: function() { return /* binding */ LayerSidebarLayout; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @swc/helpers/_/_object_spread */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_spread.js\");\n/* harmony import */ var _swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @swc/helpers/_/_object_spread_props */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_spread_props.js\");\n/* harmony import */ var _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @swc/helpers/_/_sliced_to_array */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_sliced_to_array.js\");\n/* harmony import */ var _swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @swc/helpers/_/_to_consumable_array */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_to_consumable_array.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Icon,useIsomorphicLayoutEffect!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/hooks/useIsomorphicLayoutEffect.js\");\n/* harmony import */ var _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Icon,useIsomorphicLayoutEffect!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/components/Icon/Icon.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(app-pages-browser)/../../node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_Builder__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/Builder */ \"(app-pages-browser)/./src/components/Builder/FieldEditor/FieldEditor.tsx\");\n/* harmony import */ var _contexts_BuilderContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/BuilderContext */ \"(app-pages-browser)/./src/contexts/BuilderContext.tsx\");\n/* harmony import */ var _pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pagebuilderlayout.module.scss */ \"(app-pages-browser)/./src/layouts/builder/page/pagebuilderlayout.module.scss\");\n/* harmony import */ var _pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nvar _this = undefined;\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n// Custom hook để phát hiện click bên ngoài một phần tử\nvar useClickOutside = function(callback) {\n    _s();\n    var ref = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n        var handleClickOutside = function(event) {\n            if (ref.current && !ref.current.contains(event.target)) {\n                callback();\n            }\n        };\n        document.addEventListener(\"mousedown\", handleClickOutside);\n        return function() {\n            document.removeEventListener(\"mousedown\", handleClickOutside);\n        };\n    }, [\n        callback\n    ]);\n    return ref;\n};\n_s(useClickOutside, \"8uVE59eA/r6b92xF80p7sH8rXLk=\");\nvar LayerSidebarLayout = function() {\n    _s1();\n    var context = (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(_contexts_BuilderContext__WEBPACK_IMPORTED_MODULE_4__.PageBuilderContext);\n    var childCmp = context.childComponentData, setChildComponentData = context.setChildComponentData, layerPos = context.layerPos, setActiveMediaId = context.setActiveMediaId, setMediaInfoData = context.setMediaInfoData;\n    var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)((0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0), 2), curChildIndex = _useState[0], setCurChildIndex = _useState[1];\n    var _useState1 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)((0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), 2), isPrevEntriesOpen = _useState1[0], setIsPrevEntriesOpen = _useState1[1];\n    // Sử dụng hook useClickOutside để đóng dropdown khi click ra ngoài\n    var prevEntriesRef = useClickOutside(function() {\n        if (isPrevEntriesOpen) {\n            setIsPrevEntriesOpen(false);\n        }\n    });\n    var handleBack = function(idx) {\n        var newVal = (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_6__._)(childCmp);\n        console.log(idx);\n        if (idx !== undefined) {\n            if (curChildIndex === idx) return;\n            setCurChildIndex(idx);\n            newVal.splice(idx + 1, newVal.length - (idx + 1));\n        } else {\n            setCurChildIndex(curChildIndex - 1);\n            newVal.pop();\n        }\n        setChildComponentData(newVal);\n        // Close MediaInfoLayer when LayerSidebarLayout is completely closed\n        if (newVal.length === 0) {\n            setActiveMediaId(null);\n            setMediaInfoData({\n                name: \"\",\n                url: \"\"\n            });\n        }\n    };\n    var handleRemove = function(idx) {\n        console.log(\"Remove idx:\", idx);\n        if (!childCmp[idx]) return;\n        var currentEntry = childCmp[idx];\n        // Use the handleRemove function passed from the Component\n        if (currentEntry.handleRemove && typeof currentEntry.entryIndex === \"number\") {\n            // Call the Component's handleRemove with the correct index\n            currentEntry.handleRemove(currentEntry.entryIndex);\n        }\n        // Update childComponentData - remove current and subsequent entries\n        var newChildData = (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_6__._)(childCmp);\n        newChildData.splice(idx, newChildData.length - idx);\n        setChildComponentData(newChildData);\n        // Update current index to previous level\n        if (idx > 0) {\n            setCurChildIndex(idx - 1);\n        }\n    };\n    var handleDuplicate = function(idx) {\n        console.log(\"Duplicate idx:\", idx);\n        if (!childCmp[idx]) return;\n        var currentEntry = childCmp[idx];\n        // Use the handleDuplicate function passed from the Component\n        if (currentEntry.handleDuplicate && typeof currentEntry.entryIndex === \"number\") {\n            // Call the Component's handleDuplicate with the correct index\n            currentEntry.handleDuplicate(currentEntry.entryIndex);\n        }\n    // Note: Non-repeatable components cannot be duplicated\n    };\n    (0,_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_7__.useIsomorphicLayoutEffect)(function() {\n        if (!childCmp || childCmp.length === 0) return;\n        setCurChildIndex(childCmp.length - 1);\n    }, [\n        childCmp\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: childCmp && childCmp.length > 0 && childCmp[curChildIndex] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().sidebar), (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().sidebar__layer), layerPos !== \"\" ? (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default())[layerPos] : \"\"),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().component__title),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: function() {\n                                return handleBack(undefined);\n                            },\n                            className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().back__button),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_8__.Icon, {\n                                type: \"cms\",\n                                variant: \"back\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 8\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 7\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                            className: \"collect__heading collect__heading--h6\",\n                            children: [\n                                childCmp.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().preventries),\n                                            ref: prevEntriesRef,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: function() {\n                                                        return setIsPrevEntriesOpen(!isPrevEntriesOpen);\n                                                    },\n                                                    title: \"Show previous entries\",\n                                                    className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().preventries__trigger),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                                        lineNumber: 134,\n                                                        columnNumber: 12\n                                                    }, _this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                                    lineNumber: 129,\n                                                    columnNumber: 11\n                                                }, _this),\n                                                isPrevEntriesOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().preventries__list),\n                                                    children: childCmp.map(function(item, idx) {\n                                                        return idx === ( false || curChildIndex) ? null : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().preventries__item),\n                                                            title: \"Back to \".concat(item.name),\n                                                            onClick: function() {\n                                                                handleBack(idx);\n                                                                setIsPrevEntriesOpen(false);\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: item.name\n                                                            }, idx, false, {\n                                                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                                                lineNumber: 149,\n                                                                columnNumber: 16\n                                                            }, _this)\n                                                        }, idx, false, {\n                                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                                            lineNumber: 140,\n                                                            columnNumber: 15\n                                                        }, _this);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                                    lineNumber: 137,\n                                                    columnNumber: 12\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 10\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"/\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 10\n                                        }, _this)\n                                    ]\n                                }, void 0, true),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    title: curChildIndex === 0 ? \"\" : \"Back to \".concat(childCmp[curChildIndex].name),\n                                    className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().cur__entry),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: childCmp[curChildIndex].name\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 9\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 8\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 7\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().component__action),\n                            children: [\n                                childCmp[curChildIndex].handleDuplicate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    title: \"Duplicate this entry\",\n                                    onClick: function() {\n                                        return handleDuplicate(curChildIndex);\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_8__.Icon, {\n                                        variant: \"duplicate\",\n                                        type: \"cms\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 10\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 9\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().remove__button),\n                                    title: \"Remove this entry\",\n                                    onClick: function() {\n                                        return handleRemove(curChildIndex);\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_8__.Icon, {\n                                        variant: \"remove\",\n                                        type: \"cms\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 9\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 8\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 7\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                    lineNumber: 121,\n                    columnNumber: 6\n                }, _this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_3___default().editor__components),\n                    children: childCmp[curChildIndex].fields.map(function(param) {\n                        var _param = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_5__._)(param, 2), key = _param[0], fValue = _param[1];\n                        var _childCmp_curChildIndex_value, _childCmp_curChildIndex;\n                        var fval = fValue;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Builder__WEBPACK_IMPORTED_MODULE_9__.FieldEditor, (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_10__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_11__._)({}, fval), {\n                            layerPos: layerPos,\n                            name: key,\n                            size: 12,\n                            value: (_childCmp_curChildIndex = childCmp[curChildIndex]) === null || _childCmp_curChildIndex === void 0 ? void 0 : (_childCmp_curChildIndex_value = _childCmp_curChildIndex.value) === null || _childCmp_curChildIndex_value === void 0 ? void 0 : _childCmp_curChildIndex_value[key],\n                            onChange: function(props) {\n                                var _childCmp_curChildIndex;\n                                if (!((_childCmp_curChildIndex = childCmp[curChildIndex]) === null || _childCmp_curChildIndex === void 0 ? void 0 : _childCmp_curChildIndex.onChange)) return;\n                                console.log(props, key, fval, childCmp[curChildIndex].value);\n                                childCmp[curChildIndex].onChange(props);\n                            }\n                        }), key, false, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 9\n                        }, _this);\n                    })\n                }, void 0, false, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n                    lineNumber: 181,\n                    columnNumber: 6\n                }, _this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\LayerSidebarLayout.tsx\",\n            lineNumber: 114,\n            columnNumber: 5\n        }, _this)\n    }, void 0, false);\n};\n_s1(LayerSidebarLayout, \"HOt8P3zlm4MIAp2I32GX9YJyhhg=\", false, function() {\n    return [\n        useClickOutside,\n        _barrel_optimize_names_Icon_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_7__.useIsomorphicLayoutEffect\n    ];\n});\n_c = LayerSidebarLayout;\nvar _c;\n$RefreshReg$(_c, \"LayerSidebarLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/layouts/builder/page/LayerSidebarLayout.tsx\n"));

/***/ })

});