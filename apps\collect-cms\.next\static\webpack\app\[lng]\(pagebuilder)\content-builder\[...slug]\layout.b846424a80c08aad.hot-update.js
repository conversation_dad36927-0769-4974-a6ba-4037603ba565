"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[lng]/(pagebuilder)/content-builder/[...slug]/layout",{

/***/ "(app-pages-browser)/./src/layouts/builder/page/RightSidebarLayout.tsx":
/*!*********************************************************!*\
  !*** ./src/layouts/builder/page/RightSidebarLayout.tsx ***!
  \*********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RightSidebarLayout: function() { return /* binding */ RightSidebarLayout; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_define_property__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @swc/helpers/_/_define_property */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_define_property.js\");\n/* harmony import */ var _swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @swc/helpers/_/_object_spread */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_spread.js\");\n/* harmony import */ var _swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @swc/helpers/_/_object_spread_props */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_spread_props.js\");\n/* harmony import */ var _swc_helpers_object_without_properties__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @swc/helpers/_/_object_without_properties */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_without_properties.js\");\n/* harmony import */ var _swc_helpers_to_property_key__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @swc/helpers/_/_to_property_key */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_to_property_key.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Button_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Button,useIsomorphicLayoutEffect!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/hooks/useIsomorphicLayoutEffect.js\");\n/* harmony import */ var _barrel_optimize_names_Button_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Button,useIsomorphicLayoutEffect!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/components/Button/Button.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(app-pages-browser)/../../node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_Builder__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/Builder */ \"(app-pages-browser)/./src/components/Builder/FieldEditor/FieldEditor.tsx\");\n/* harmony import */ var _contexts_BuilderContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/BuilderContext */ \"(app-pages-browser)/./src/contexts/BuilderContext.tsx\");\n/* harmony import */ var _LayerSidebarLayout__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./LayerSidebarLayout */ \"(app-pages-browser)/./src/layouts/builder/page/LayerSidebarLayout.tsx\");\n/* harmony import */ var _pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./pagebuilderlayout.module.scss */ \"(app-pages-browser)/./src/layouts/builder/page/pagebuilderlayout.module.scss\");\n/* harmony import */ var _pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_4__);\n\n\n\n\n\nvar _this = undefined;\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nvar RightSidebarLayout = function() {\n    var _childComponentData_;\n    _s();\n    var context = (0,react__WEBPACK_IMPORTED_MODULE_3__.useContext)(_contexts_BuilderContext__WEBPACK_IMPORTED_MODULE_5__.PageBuilderContext);\n    var expandedSidebar = context.expandedSidebar, contentType = context.contentType, configuration = context.configuration, data = context.data, setData = context.setData, childComponentData = context.childComponentData, setChildComponentData = context.setChildComponentData, setActiveMediaId = context.setActiveMediaId, setMediaInfoData = context.setMediaInfoData;\n    var _ref = data || {}, commonData = _ref.data;\n    var router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    var pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    var globalFields = (0,react__WEBPACK_IMPORTED_MODULE_3__.useMemo)(function() {\n        if (!contentType.data || !configuration.data) return [];\n        var _configuration_data_contentType = configuration.data.contentType, layouts = _configuration_data_contentType.layouts, settings = _configuration_data_contentType.settings;\n        var mainFieldKey = settings.mainField;\n        var _contentType_data_schema_attributes = contentType.data.schema.attributes, mainField = _contentType_data_schema_attributes[mainFieldKey], components = _contentType_data_schema_attributes.components, fields = (0,_swc_helpers_object_without_properties__WEBPACK_IMPORTED_MODULE_6__._)(_contentType_data_schema_attributes, [\n            mainFieldKey,\n            \"components\"\n        ].map(_swc_helpers_to_property_key__WEBPACK_IMPORTED_MODULE_7__._));\n        var normalizedFields = layouts.edit.flat().filter(function(item) {\n            return ![\n                \"components\",\n                mainFieldKey\n            ].includes(item.name);\n        }).map(function(item) {\n            return (0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, item, fields[item.name]);\n        }).filter(function(item) {\n            return \"type\" in item;\n        });\n        return normalizedFields;\n    }, [\n        contentType,\n        configuration\n    ]);\n    (0,_barrel_optimize_names_Button_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_9__.useIsomorphicLayoutEffect)(function() {\n        !expandedSidebar.right && setChildComponentData([]);\n    }, [\n        expandedSidebar.right\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_4___default().sidebar), !expandedSidebar.right && (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_4___default().is__hidden)),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_10__.Button, {\n                className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"collect__button\", \"collect__button--lg\", \"black\"),\n                onClick: function() {\n                    var newPath = pathname.replace(\"content-builder\", \"content-manager\").split(\"/\");\n                    if (newPath[newPath.length - 1] !== \"edit\") {\n                        newPath.push(\"edit\");\n                    }\n                    router.push(newPath.join(\"/\"));\n                },\n                children: \"Switch to Manager Mode\"\n            }, void 0, false, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\RightSidebarLayout.tsx\",\n                lineNumber: 58,\n                columnNumber: 4\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_4___default().component__wrapper),\n                children: [\n                    childComponentData && ((_childComponentData_ = childComponentData[0]) === null || _childComponentData_ === void 0 ? void 0 : _childComponentData_.name) !== \"\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LayerSidebarLayout__WEBPACK_IMPORTED_MODULE_11__.LayerSidebarLayout, {}, void 0, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\RightSidebarLayout.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 66\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_4___default().component__title),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                            className: \"collect__heading collect__heading--h5\",\n                            children: \"Page Settings\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\RightSidebarLayout.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 6\n                        }, _this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\RightSidebarLayout.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 5\n                    }, _this),\n                    globalFields === null || globalFields === void 0 ? void 0 : globalFields.map(function(field, idx) {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Builder__WEBPACK_IMPORTED_MODULE_12__.FieldEditor, (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_13__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, field), {\n                            layerPos: \"right\",\n                            value: commonData && commonData[field.name],\n                            onChange: function(props) {\n                                setData(function(prevData) {\n                                    var newData = (0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, prevData);\n                                    var _$field = props.field, value = props.value;\n                                    newData.data = (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_13__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, newData.data), (0,_swc_helpers_define_property__WEBPACK_IMPORTED_MODULE_14__._)({}, _$field.trim(), value));\n                                    console.log(\"[FieldEditor] New data after update:\", newData);\n                                    return newData;\n                                });\n                            }\n                        }), idx, false, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\RightSidebarLayout.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 6\n                        }, _this);\n                    })\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\RightSidebarLayout.tsx\",\n                lineNumber: 70,\n                columnNumber: 4\n            }, _this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\RightSidebarLayout.tsx\",\n        lineNumber: 57,\n        columnNumber: 3\n    }, _this);\n};\n_s(RightSidebarLayout, \"YtHEQYDdwA8ymYZ75lBB3KCamkk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        _barrel_optimize_names_Button_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_9__.useIsomorphicLayoutEffect\n    ];\n});\n_c = RightSidebarLayout;\nvar _c;\n$RefreshReg$(_c, \"RightSidebarLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/layouts/builder/page/RightSidebarLayout.tsx\n"));

/***/ })

});