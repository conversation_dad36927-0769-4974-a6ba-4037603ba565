"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[lng]/(pagebuilder)/content-builder/[...slug]/layout",{

/***/ "(app-pages-browser)/./src/layouts/builder/page/RightSidebarLayout.tsx":
/*!*********************************************************!*\
  !*** ./src/layouts/builder/page/RightSidebarLayout.tsx ***!
  \*********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RightSidebarLayout: function() { return /* binding */ RightSidebarLayout; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_define_property__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @swc/helpers/_/_define_property */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_define_property.js\");\n/* harmony import */ var _swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @swc/helpers/_/_object_spread */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_spread.js\");\n/* harmony import */ var _swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @swc/helpers/_/_object_spread_props */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_spread_props.js\");\n/* harmony import */ var _swc_helpers_object_without_properties__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @swc/helpers/_/_object_without_properties */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_without_properties.js\");\n/* harmony import */ var _swc_helpers_to_property_key__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @swc/helpers/_/_to_property_key */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_to_property_key.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Button_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Button,useIsomorphicLayoutEffect!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/hooks/useIsomorphicLayoutEffect.js\");\n/* harmony import */ var _barrel_optimize_names_Button_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Button,useIsomorphicLayoutEffect!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/components/Button/Button.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(app-pages-browser)/../../node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_Builder__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/Builder */ \"(app-pages-browser)/./src/components/Builder/FieldEditor/FieldEditor.tsx\");\n/* harmony import */ var _contexts_BuilderContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/BuilderContext */ \"(app-pages-browser)/./src/contexts/BuilderContext.tsx\");\n/* harmony import */ var _LayerSidebarLayout__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./LayerSidebarLayout */ \"(app-pages-browser)/./src/layouts/builder/page/LayerSidebarLayout.tsx\");\n/* harmony import */ var _pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./pagebuilderlayout.module.scss */ \"(app-pages-browser)/./src/layouts/builder/page/pagebuilderlayout.module.scss\");\n/* harmony import */ var _pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_4__);\n\n\n\n\n\nvar _this = undefined;\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nvar RightSidebarLayout = function() {\n    var _childComponentData_;\n    _s();\n    var context = (0,react__WEBPACK_IMPORTED_MODULE_3__.useContext)(_contexts_BuilderContext__WEBPACK_IMPORTED_MODULE_5__.PageBuilderContext);\n    var expandedSidebar = context.expandedSidebar, contentType = context.contentType, configuration = context.configuration, data = context.data, setData = context.setData, childComponentData = context.childComponentData, setChildComponentData = context.setChildComponentData, setActiveMediaId = context.setActiveMediaId, setMediaInfoData = context.setMediaInfoData;\n    var _ref = data || {}, commonData = _ref.data;\n    var router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    var pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    var globalFields = (0,react__WEBPACK_IMPORTED_MODULE_3__.useMemo)(function() {\n        if (!contentType.data || !configuration.data) return [];\n        var _configuration_data_contentType = configuration.data.contentType, layouts = _configuration_data_contentType.layouts, settings = _configuration_data_contentType.settings;\n        var mainFieldKey = settings.mainField;\n        var _contentType_data_schema_attributes = contentType.data.schema.attributes, mainField = _contentType_data_schema_attributes[mainFieldKey], components = _contentType_data_schema_attributes.components, fields = (0,_swc_helpers_object_without_properties__WEBPACK_IMPORTED_MODULE_6__._)(_contentType_data_schema_attributes, [\n            mainFieldKey,\n            \"components\"\n        ].map(_swc_helpers_to_property_key__WEBPACK_IMPORTED_MODULE_7__._));\n        var normalizedFields = layouts.edit.flat().filter(function(item) {\n            return ![\n                \"components\",\n                mainFieldKey\n            ].includes(item.name);\n        }).map(function(item) {\n            return (0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, item, fields[item.name]);\n        }).filter(function(item) {\n            return \"type\" in item;\n        });\n        return normalizedFields;\n    }, [\n        contentType,\n        configuration\n    ]);\n    (0,_barrel_optimize_names_Button_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_9__.useIsomorphicLayoutEffect)(function() {\n        if (!expandedSidebar.right) {\n            setChildComponentData([]);\n            // Close MediaInfoLayer when right sidebar is closed\n            setActiveMediaId(null);\n            setMediaInfoData({\n                name: \"\",\n                url: \"\"\n            });\n        }\n    }, [\n        expandedSidebar.right\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_4___default().sidebar), !expandedSidebar.right && (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_4___default().is__hidden)),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_10__.Button, {\n                className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"collect__button\", \"collect__button--lg\", \"black\"),\n                onClick: function() {\n                    var newPath = pathname.replace(\"content-builder\", \"content-manager\").split(\"/\");\n                    if (newPath[newPath.length - 1] !== \"edit\") {\n                        newPath.push(\"edit\");\n                    }\n                    router.push(newPath.join(\"/\"));\n                },\n                children: \"Switch to Manager Mode\"\n            }, void 0, false, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\RightSidebarLayout.tsx\",\n                lineNumber: 63,\n                columnNumber: 4\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_4___default().component__wrapper),\n                children: [\n                    childComponentData && ((_childComponentData_ = childComponentData[0]) === null || _childComponentData_ === void 0 ? void 0 : _childComponentData_.name) !== \"\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LayerSidebarLayout__WEBPACK_IMPORTED_MODULE_11__.LayerSidebarLayout, {}, void 0, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\RightSidebarLayout.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 66\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_pagebuilderlayout_module_scss__WEBPACK_IMPORTED_MODULE_4___default().component__title),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                            className: \"collect__heading collect__heading--h5\",\n                            children: \"Page Settings\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\RightSidebarLayout.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 6\n                        }, _this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\RightSidebarLayout.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 5\n                    }, _this),\n                    globalFields === null || globalFields === void 0 ? void 0 : globalFields.map(function(field, idx) {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Builder__WEBPACK_IMPORTED_MODULE_12__.FieldEditor, (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_13__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, field), {\n                            layerPos: \"right\",\n                            value: commonData && commonData[field.name],\n                            onChange: function(props) {\n                                setData(function(prevData) {\n                                    var newData = (0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, prevData);\n                                    var _$field = props.field, value = props.value;\n                                    newData.data = (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_13__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_8__._)({}, newData.data), (0,_swc_helpers_define_property__WEBPACK_IMPORTED_MODULE_14__._)({}, _$field.trim(), value));\n                                    console.log(\"[FieldEditor] New data after update:\", newData);\n                                    return newData;\n                                });\n                            }\n                        }), idx, false, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\RightSidebarLayout.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 6\n                        }, _this);\n                    })\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\RightSidebarLayout.tsx\",\n                lineNumber: 75,\n                columnNumber: 4\n            }, _this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\layouts\\\\builder\\\\page\\\\RightSidebarLayout.tsx\",\n        lineNumber: 62,\n        columnNumber: 3\n    }, _this);\n};\n_s(RightSidebarLayout, \"YtHEQYDdwA8ymYZ75lBB3KCamkk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        _barrel_optimize_names_Button_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_9__.useIsomorphicLayoutEffect\n    ];\n});\n_c = RightSidebarLayout;\nvar _c;\n$RefreshReg$(_c, \"RightSidebarLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/layouts/builder/page/RightSidebarLayout.tsx\n"));

/***/ })

});